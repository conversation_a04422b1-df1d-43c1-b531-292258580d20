<script lang="ts">
    import { Button } from '@/components/ui/button';
    import { ChevronDown, ChevronRight, Brain } from 'lucide-svelte';
    
    interface Props {
        content: string;
    }
    
    let { content }: Props = $props();
    
    let showReasoning = $state(false);
    
    // Função para processar o conteúdo e separar reasoning do output
    const processedContent = $derived(() => {
        const thinkRegex = /<think>([\s\S]*?)<\/think>/gi;
        const matches = [...content.matchAll(thinkRegex)];
        
        if (matches.length === 0) {
            return {
                hasReasoning: false,
                reasoning: '',
                output: content
            };
        }
        
        // Extrair todo o conteúdo das tags <think>
        const reasoning = matches.map(match => match[1].trim()).join('\n\n');
        
        // Remover as tags <think> do conteúdo principal
        const output = content.replace(thinkRegex, '').trim();
        
        return {
            hasReasoning: true,
            reasoning,
            output
        };
    });
</script>

<div class="space-y-3">
    <!-- Reasoning Section (colapsável) -->
    {#if processedContent.hasReasoning}
        <div class="border rounded-lg bg-muted/30">
            <Button
                variant="ghost"
                size="sm"
                class="w-full justify-start p-3 h-auto font-normal"
                onclick={() => showReasoning = !showReasoning}
            >
                <div class="flex items-center gap-2 text-sm">
                    {#if showReasoning}
                        <ChevronDown class="h-4 w-4" />
                    {:else}
                        <ChevronRight class="h-4 w-4" />
                    {/if}
                    <Brain class="h-4 w-4 text-muted-foreground" />
                    <span class="text-muted-foreground">Reasoning</span>
                    <span class="text-xs text-muted-foreground/70">
                        ({processedContent.reasoning.split('\n').length} lines)
                    </span>
                </div>
            </Button>
            
            {#if showReasoning}
                <div class="px-3 pb-3">
                    <div class="prose prose-sm max-w-none dark:prose-invert">
                        <pre class="whitespace-pre-wrap text-sm bg-background/50 rounded p-3 border">{processedContent.reasoning}</pre>
                    </div>
                </div>
            {/if}
        </div>
    {/if}
    
    <!-- Main Output -->
    {#if processedContent.output}
        <div class="prose prose-sm max-w-none dark:prose-invert">
            <pre class="whitespace-pre-wrap text-sm">{processedContent.output}</pre>
        </div>
    {/if}
</div>
